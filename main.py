#!/usr/bin/env python3
"""
基于 PySide6 的现代化桌面应用程序
视频创作工具 - 主入口文件
"""

import sys
import json
import os
import datetime
import requests
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QStackedWidget, QComboBox,
    QCheckBox, QMessageBox, QTextEdit, QLineEdit, QGroupBox,
    QSplitter, QFileDialog, QProgressBar, QScrollArea, QPlainTextEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, QSettings, QTimer, QThread, Signal, QSize
from PySide6.QtGui import QFont, QColor


class ModernApp(QMainWindow):
    """主应用程序类"""

    def __init__(self):
        super().__init__()

        # 应用程序设置
        self.settings = QSettings("VideoCreator", "ModernApp")
        self.settings_file = "app_settings.json"

        # 加载设置
        self.app_settings = self.load_settings()

        # 初始化语言系统
        self.init_language_system()

        # 初始化界面
        self.init_ui()
        self.setup_styles()
        self.create_pages()

        # 显示默认页面
        self.show_page("settings")

    def closeEvent(self, event):
        """应用程序关闭事件"""
        # 保存设置
        self.save_settings()

        # 清理所有页面的工作线程
        for page_name, page in self.pages.items():
            if hasattr(page, 'cleanup_threads'):
                try:
                    page.cleanup_threads()
                except:
                    pass

        event.accept()

    def init_language_system(self):
        """初始化语言系统"""
        self.texts = {
            "中文": {
                "app_title": "视频创作工具",
                "settings": "设置",
                "ollama_test": "Ollama 测试",
                "ollama_manager": "模型管理",
                "version": "版本 1.0.0",
                "basic_settings": "基本设置",
                "language": "语言:",
                "theme": "主题:",
                "auto_save": "自动保存:",
                "save_settings": "💾 保存设置",
                "reset_default": "🔄 重置默认",
                "export_settings": "📤 导出设置",
                "app_info": "应用信息",
                "app_name": "应用名称: 视频创作工具",
                "app_version": "版本: 1.0.0 (PySide6)",
                "settings_file": "设置文件:",
                "current_theme": "当前主题:",
                "current_language": "语言:",
                "auto_save_status": "自动保存:",
                "enabled": "启用",
                "disabled": "禁用",
                "connection_test": "连接测试",
                "server_address": "服务器地址:",
                "test_connection": "🔍 测试连接",
                "test_results": "测试结果",
                "testing": "测试中...",
                "retest": "🔄 重新测试",
                "language_changed": "语言已更改为:",
                "theme_changed": "主题已更改为:",
                "auto_save_enabled": "自动保存已启用",
                "auto_save_disabled": "自动保存已禁用",
                "settings_saved": "设置已保存！",
                "save_failed": "保存设置失败！",
                "confirm_reset": "确定要重置所有设置为默认值吗？",
                "reset_success": "设置已重置为默认值！",
                "export_settings_title": "导出设置",
                "export_success": "设置已导出到:",
                "export_failed": "导出设置失败:",
                "warning": "警告",
                "enter_server_address": "请输入服务器地址",
                "ollama_settings": "Ollama 设置",
                "server_address": "服务器地址:",
                "default_model": "默认模型:",
                "model_management": "模型管理",
                "refresh_models": "刷新模型列表",
                "delete_model": "删除选中模型",
                "create_model": "创建自定义模型",
                "model_name": "模型名称:",
                "base_model": "基础模型:",
                "model_file": "模型文件:",
                "confirm_delete": "确定要删除模型",
                "delete_success": "模型删除成功",
                "delete_failed": "模型删除失败",
                "create_success": "模型创建成功",
                "create_failed": "模型创建失败"
            },
            "English": {
                "app_title": "Video Creator Tool",
                "settings": "Settings",
                "ollama_test": "Ollama Test",
                "ollama_manager": "Model Manager",
                "version": "Version 1.0.0",
                "basic_settings": "Basic Settings",
                "language": "Language:",
                "theme": "Theme:",
                "auto_save": "Auto Save:",
                "save_settings": "💾 Save Settings",
                "reset_default": "🔄 Reset Default",
                "export_settings": "📤 Export Settings",
                "app_info": "Application Info",
                "app_name": "App Name: Video Creator Tool",
                "app_version": "Version: 1.0.0 (PySide6)",
                "settings_file": "Settings File:",
                "current_theme": "Current Theme:",
                "current_language": "Language:",
                "auto_save_status": "Auto Save:",
                "enabled": "Enabled",
                "disabled": "Disabled",
                "connection_test": "Connection Test",
                "server_address": "Server Address:",
                "test_connection": "🔍 Test Connection",
                "test_results": "Test Results",
                "testing": "Testing...",
                "retest": "🔄 Retest",
                "language_changed": "Language changed to:",
                "theme_changed": "Theme changed to:",
                "auto_save_enabled": "Auto save enabled",
                "auto_save_disabled": "Auto save disabled",
                "settings_saved": "Settings saved!",
                "save_failed": "Failed to save settings!",
                "confirm_reset": "Are you sure you want to reset all settings to default?",
                "reset_success": "Settings have been reset to default!",
                "export_settings_title": "Export Settings",
                "export_success": "Settings exported to:",
                "export_failed": "Failed to export settings:",
                "warning": "Warning",
                "enter_server_address": "Please enter server address",
                "ollama_settings": "Ollama Settings",
                "server_address": "Server Address:",
                "default_model": "Default Model:",
                "model_management": "Model Management",
                "refresh_models": "Refresh Models",
                "delete_model": "Delete Selected Model",
                "create_model": "Create Custom Model",
                "model_name": "Model Name:",
                "base_model": "Base Model:",
                "model_file": "Model File:",
                "confirm_delete": "Are you sure you want to delete model",
                "delete_success": "Model deleted successfully",
                "delete_failed": "Failed to delete model",
                "create_success": "Model created successfully",
                "create_failed": "Failed to create model"
            }
        }

        # 设置当前语言
        self.current_language = self.app_settings.get('language', '中文')

    def get_text(self, key):
        """获取当前语言的文本"""
        return self.texts.get(self.current_language, self.texts["中文"]).get(key, key)

    def update_language(self, language):
        """更新语言并刷新界面"""
        self.current_language = language
        self.app_settings['language'] = language

        # 更新窗口标题
        self.setWindowTitle(self.get_text("app_title"))

        # 更新所有页面的文本
        self.refresh_all_texts()

    def refresh_all_texts(self):
        """刷新所有界面文本"""
        # 更新侧边栏文本
        self.title_label.setText(self.get_text("app_title"))
        self.settings_btn.setText(f"⚙️ {self.get_text('settings')}")
        self.ollama_btn.setText(f"🤖 {self.get_text('ollama_test')}")
        self.ollama_manager_btn.setText(f"📋 {self.get_text('ollama_manager')}")
        self.version_label.setText(self.get_text("version"))

        # 更新页面文本
        for page_name, page in self.pages.items():
            if hasattr(page, 'refresh_texts'):
                page.refresh_texts()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(self.get_text("app_title"))
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(900, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建侧边栏
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # 创建内容区域
        self.content_area = QStackedWidget()
        splitter.addWidget(self.content_area)
        
        # 设置分割器比例
        splitter.setSizes([280, 920])
        splitter.setCollapsible(0, False)
    
    def create_sidebar(self):
        """创建侧边栏"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(280)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo 区域
        logo_widget = self.create_logo_section()
        layout.addWidget(logo_widget)
        
        # 菜单区域
        menu_widget = self.create_menu_section()
        layout.addWidget(menu_widget)
        
        # 弹性空间
        layout.addStretch()
        
        # 底部信息
        info_widget = self.create_info_section()
        layout.addWidget(info_widget)
        
        return sidebar
    
    def create_logo_section(self):
        """创建 Logo 区域"""
        widget = QFrame()
        widget.setObjectName("logoSection")
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 30, 20, 20)
        layout.setSpacing(10)
        
        # Logo 图标
        logo_label = QLabel("🎬")
        logo_label.setObjectName("logoIcon")
        logo_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(logo_label)
        
        # 应用名称
        self.title_label = QLabel(self.get_text("app_title"))
        self.title_label.setObjectName("appTitle")
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)
        
        return widget
    
    def create_menu_section(self):
        """创建菜单区域"""
        widget = QFrame()
        widget.setObjectName("menuSection")
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # 设置按钮
        self.settings_btn = QPushButton(f"⚙️ {self.get_text('settings')}")
        self.settings_btn.setObjectName("menuButton")
        self.settings_btn.clicked.connect(lambda: self.show_page("settings"))
        layout.addWidget(self.settings_btn)

        # Ollama 测试按钮
        self.ollama_btn = QPushButton(f"🤖 {self.get_text('ollama_test')}")
        self.ollama_btn.setObjectName("menuButton")
        self.ollama_btn.clicked.connect(lambda: self.show_page("ollama"))
        layout.addWidget(self.ollama_btn)

        # Ollama 模型管理按钮
        self.ollama_manager_btn = QPushButton(f"📋 {self.get_text('ollama_manager')}")
        self.ollama_manager_btn.setObjectName("menuButton")
        self.ollama_manager_btn.clicked.connect(lambda: self.show_page("ollama_manager"))
        layout.addWidget(self.ollama_manager_btn)
        
        return widget
    
    def create_info_section(self):
        """创建信息区域"""
        widget = QFrame()
        widget.setObjectName("infoSection")
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 30)
        
        self.version_label = QLabel(self.get_text("version"))
        self.version_label.setObjectName("versionLabel")
        self.version_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.version_label)
        
        return widget
    
    def load_settings(self):
        """加载应用设置"""
        default_settings = {
            "language": "中文",
            "theme": "现代",
            "window_size": "1200x800",
            "auto_save": True,
            "ollama_server": "http://localhost:11434",
            "default_model": "llama3"
        }
        
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                # 合并默认设置
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
            else:
                return default_settings
        except Exception as e:
            print(f"加载设置失败: {e}")
            return default_settings
    
    def save_settings(self):
        """保存应用设置"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.app_settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存设置失败: {e}")
            return False
    
    def update_setting(self, key, value):
        """更新设置"""
        self.app_settings[key] = value
        if self.app_settings.get('auto_save', True):
            self.save_settings()
    
    def setup_styles(self):
        """设置应用样式"""
        theme = self.app_settings.get('theme', '现代')
        self.apply_theme(theme)
    
    def apply_theme(self, theme_name):
        """应用主题"""
        if theme_name == "现代":
            style = self.get_modern_style()
        elif theme_name == "暗色":
            style = self.get_dark_style()
        elif theme_name == "经典":
            style = self.get_classic_style()
        else:
            style = self.get_modern_style()
        
        self.setStyleSheet(style)

    def get_modern_style(self):
        """现代主题样式"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #f8f9fa;
        }

        /* 侧边栏 */
        QFrame#sidebar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2c3e50, stop:1 #34495e);
            border-right: 2px solid #34495e;
        }

        /* Logo 区域 */
        QFrame#logoSection {
            background: transparent;
            border-bottom: 1px solid #34495e;
        }

        QLabel#logoIcon {
            font-size: 36px;
            color: #ffffff;
            background: qradialgradient(cx:0.5, cy:0.5, radius:0.5,
                fx:0.5, fy:0.5, stop:0 rgba(52, 152, 219, 100),
                stop:1 transparent);
            border-radius: 25px;
            padding: 10px;
        }

        QLabel#appTitle {
            font-size: 18px;
            font-weight: bold;
            color: #ecf0f1;
        }

        /* 菜单区域 */
        QFrame#menuSection {
            background: transparent;
        }

        QPushButton#menuButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #34495e, stop:1 #2c3e50);
            color: #ecf0f1;
            border: 1px solid #4a6741;
            padding: 15px 20px;
            text-align: left;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            margin: 2px;
        }

        QPushButton#menuButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
            border-color: #5dade2;
        }

        QPushButton#menuButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2980b9, stop:1 #1f618d);
            border-color: #2980b9;
        }

        /* 信息区域 */
        QFrame#infoSection {
            background: transparent;
            border-top: 1px solid #34495e;
        }

        QLabel#versionLabel {
            color: #95a5a6;
            font-size: 11px;
            font-style: italic;
        }

        /* 内容区域 */
        QStackedWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
            border-radius: 10px;
            margin: 5px;
        }

        /* 通用控件样式 */
        QComboBox {
            background-color: #ffffff;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            min-width: 120px;
        }

        QComboBox:hover {
            border-color: #3498db;
        }

        QComboBox:focus {
            border-color: #2980b9;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #7f8c8d;
            margin-right: 5px;
        }

        QCheckBox {
            font-size: 14px;
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: #ffffff;
        }

        QCheckBox::indicator:hover {
            border-color: #3498db;
        }

        QCheckBox::indicator:checked {
            background-color: #3498db;
            border-color: #2980b9;
        }

        QCheckBox::indicator:checked:hover {
            background-color: #2980b9;
        }
        """

    def get_dark_style(self):
        """暗色主题样式"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #0d1117;
            color: #f0f6fc;
        }

        /* 侧边栏 */
        QFrame#sidebar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #161b22, stop:1 #21262d);
            border-right: 2px solid #30363d;
        }

        QLabel#logoIcon {
            font-size: 36px;
            color: #58a6ff;
            background: qradialgradient(cx:0.5, cy:0.5, radius:0.5,
                fx:0.5, fy:0.5, stop:0 rgba(88, 166, 255, 50),
                stop:1 transparent);
            border-radius: 25px;
            padding: 10px;
        }

        QLabel#appTitle {
            font-size: 18px;
            font-weight: bold;
            color: #f0f6fc;
        }

        QPushButton#menuButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #30363d, stop:1 #21262d);
            color: #f0f6fc;
            border: 1px solid #484f58;
            padding: 15px 20px;
            text-align: left;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            margin: 2px;
        }

        QPushButton#menuButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #58a6ff, stop:1 #1f6feb);
            border-color: #58a6ff;
            color: #ffffff;
        }

        QLabel#versionLabel {
            color: #8b949e;
            font-size: 11px;
            font-style: italic;
        }

        QStackedWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0d1117, stop:1 #161b22);
            border-radius: 10px;
            margin: 5px;
        }

        QComboBox {
            background-color: #21262d;
            border: 2px solid #30363d;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            min-width: 120px;
            color: #f0f6fc;
        }

        QComboBox:hover {
            border-color: #58a6ff;
        }

        QCheckBox {
            font-size: 14px;
            spacing: 8px;
            color: #f0f6fc;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #30363d;
            border-radius: 4px;
            background-color: #21262d;
        }

        QCheckBox::indicator:checked {
            background-color: #58a6ff;
            border-color: #1f6feb;
        }

        QLabel#pageTitle {
            color: #f0f6fc !important;
        }

        QGroupBox {
            color: #f0f6fc !important;
            border-color: #30363d !important;
        }

        QGroupBox::title {
            background-color: #0d1117 !important;
        }
        """

    def get_classic_style(self):
        """经典主题样式"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #f0f0f0;
        }

        /* 侧边栏 */
        QFrame#sidebar {
            background-color: #e8e8e8;
            border-right: 1px solid #d0d0d0;
        }

        /* Logo 区域 */
        QFrame#logoSection {
            background: transparent;
            border-bottom: 1px solid #d0d0d0;
        }

        QLabel#logoIcon {
            font-size: 32px;
            color: #333333;
            padding: 10px;
        }

        QLabel#appTitle {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
        }

        /* 菜单区域 */
        QFrame#menuSection {
            background: transparent;
        }

        QPushButton#menuButton {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            padding: 12px 16px;
            text-align: left;
            font-size: 14px;
            border-radius: 4px;
            margin: 2px;
        }

        QPushButton#menuButton:hover {
            background-color: #0078d4;
            color: #ffffff;
            border-color: #0078d4;
        }

        QPushButton#menuButton:pressed {
            background-color: #106ebe;
        }

        /* 信息区域 */
        QFrame#infoSection {
            background: transparent;
            border-top: 1px solid #d0d0d0;
        }

        QLabel#versionLabel {
            color: #666666;
            font-size: 11px;
            font-style: italic;
        }

        /* 内容区域 */
        QStackedWidget {
            background-color: #ffffff;
            border-radius: 10px;
            margin: 5px;
        }

        /* 通用控件样式 - 经典版本 */
        QComboBox {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 14px;
            min-width: 120px;
            color: #333333;
        }

        QComboBox:hover {
            border-color: #0078d4;
        }

        QComboBox:focus {
            border-color: #0078d4;
            outline: none;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #666666;
            margin-right: 5px;
        }

        QCheckBox {
            font-size: 14px;
            spacing: 8px;
            color: #333333;
        }

        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #cccccc;
            border-radius: 2px;
            background-color: #ffffff;
        }

        QCheckBox::indicator:hover {
            border-color: #0078d4;
        }

        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        QCheckBox::indicator:checked:hover {
            background-color: #106ebe;
        }

        /* 经典主题的页面标题 */
        QLabel#pageTitle {
            color: #333333 !important;
        }

        /* 经典主题的组框 */
        QGroupBox {
            color: #333333 !important;
            border-color: #cccccc !important;
        }

        QGroupBox::title {
            background-color: #ffffff !important;
        }

        /* 经典主题的文本框 */
        QLineEdit {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px;
            font-size: 14px;
            color: #333333;
        }

        QLineEdit:focus {
            border-color: #0078d4;
        }

        QTextEdit {
            background-color: #f8f9fa;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            padding: 8px;
        }
        """

    def create_pages(self):
        """创建所有页面"""
        self.pages = {}

        # 设置页面
        self.pages["settings"] = SettingsPage(self)
        self.content_area.addWidget(self.pages["settings"])

        # Ollama 测试页面
        self.pages["ollama"] = OllamaTestPage(self)
        self.content_area.addWidget(self.pages["ollama"])

        # Ollama 模型管理页面
        self.pages["ollama_manager"] = OllamaManagerPage(self)
        self.content_area.addWidget(self.pages["ollama_manager"])

    def show_page(self, page_name):
        """显示指定页面"""
        if page_name in self.pages:
            self.content_area.setCurrentWidget(self.pages[page_name])


class BasePage(QWidget):
    """页面基类"""

    def __init__(self, app):
        super().__init__()
        self.app = app
        self.init_ui()

    def init_ui(self):
        """初始化界面 - 子类需要重写"""
        pass


class SettingsPage(BasePage):
    """设置页面"""

    def init_ui(self):
        # 创建滚动区域
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_area.setWidget(scroll_content)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # 滚动内容布局
        layout = QVBoxLayout(scroll_content)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        self.title = QLabel(self.app.get_text("settings"))
        self.title.setObjectName("pageTitle")
        self.title.setStyleSheet("""
            QLabel#pageTitle {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.title)

        # 基本设置组
        basic_group = self.create_basic_settings_group()
        layout.addWidget(basic_group)

        # Ollama 设置组
        ollama_group = self.create_ollama_settings_group()
        layout.addWidget(ollama_group)

        # 应用信息组
        info_group = self.create_info_group()
        layout.addWidget(info_group)

        # 弹性空间
        layout.addStretch()

    def create_basic_settings_group(self):
        """创建基本设置组"""
        self.basic_group = QGroupBox(self.app.get_text("basic_settings"))
        self.basic_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(self.basic_group)
        layout.setSpacing(15)

        # 语言设置
        lang_layout = QHBoxLayout()
        self.lang_label = QLabel(self.app.get_text("language"))
        self.lang_label.setStyleSheet("font-weight: normal; font-size: 14px;")
        self.lang_combo = QComboBox()
        self.lang_combo.addItems(["中文", "English"])
        self.lang_combo.setCurrentText(self.app.app_settings.get('language', '中文'))
        self.lang_combo.currentTextChanged.connect(self.on_language_change)

        lang_layout.addWidget(self.lang_label)
        lang_layout.addStretch()
        lang_layout.addWidget(self.lang_combo)
        layout.addLayout(lang_layout)

        # 主题设置
        theme_layout = QHBoxLayout()
        self.theme_label = QLabel(self.app.get_text("theme"))
        self.theme_label.setStyleSheet("font-weight: normal; font-size: 14px;")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["现代", "暗色", "经典"])
        self.theme_combo.setCurrentText(self.app.app_settings.get('theme', '现代'))
        self.theme_combo.currentTextChanged.connect(self.on_theme_change)

        theme_layout.addWidget(self.theme_label)
        theme_layout.addStretch()
        theme_layout.addWidget(self.theme_combo)
        layout.addLayout(theme_layout)

        # 自动保存设置
        auto_save_layout = QHBoxLayout()
        self.auto_save_label = QLabel(self.app.get_text("auto_save"))
        self.auto_save_label.setStyleSheet("font-weight: normal; font-size: 14px;")
        self.auto_save_check = QCheckBox()
        self.auto_save_check.setChecked(self.app.app_settings.get('auto_save', True))
        self.auto_save_check.toggled.connect(self.on_auto_save_change)

        auto_save_layout.addWidget(self.auto_save_label)
        auto_save_layout.addStretch()
        auto_save_layout.addWidget(self.auto_save_check)
        layout.addLayout(auto_save_layout)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.save_btn = QPushButton(self.app.get_text("save_settings"))
        self.save_btn.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))
        self.save_btn.clicked.connect(self.save_settings)

        self.reset_btn = QPushButton(self.app.get_text("reset_default"))
        self.reset_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))
        self.reset_btn.clicked.connect(self.reset_settings)

        self.export_btn = QPushButton(self.app.get_text("export_settings"))
        self.export_btn.setStyleSheet(self.get_button_style("#27ae60", "#229954"))
        self.export_btn.clicked.connect(self.export_settings)

        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        return self.basic_group

    def get_button_style(self, color1, color2):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
        """

    def create_ollama_settings_group(self):
        """创建 Ollama 设置组"""
        self.ollama_group = QGroupBox(self.app.get_text("ollama_settings"))
        self.ollama_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(self.ollama_group)
        layout.setSpacing(15)

        # Ollama 服务器地址设置
        server_layout = QHBoxLayout()
        self.ollama_server_label = QLabel(self.app.get_text("server_address"))
        self.ollama_server_label.setStyleSheet("font-weight: normal; font-size: 14px;")

        self.ollama_server_input = QLineEdit()
        self.ollama_server_input.setText(self.app.app_settings.get('ollama_server', 'http://localhost:11434'))
        self.ollama_server_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        self.ollama_server_input.textChanged.connect(self.on_ollama_server_change)

        server_layout.addWidget(self.ollama_server_label)
        server_layout.addWidget(self.ollama_server_input)
        layout.addLayout(server_layout)

        # 默认模型设置
        model_layout = QHBoxLayout()
        self.default_model_label = QLabel(self.app.get_text("default_model"))
        self.default_model_label.setStyleSheet("font-weight: normal; font-size: 14px;")

        self.default_model_combo = QComboBox()
        self.default_model_combo.addItems(["llama3", "gemma", "codellama", "mistral"])
        self.default_model_combo.setCurrentText(self.app.app_settings.get('default_model', 'llama3'))
        self.default_model_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
                background-color: #ffffff;
                min-width: 120px;
            }
            QComboBox:hover {
                border-color: #3498db;
            }
        """)
        self.default_model_combo.currentTextChanged.connect(self.on_default_model_change)

        model_layout.addWidget(self.default_model_label)
        model_layout.addWidget(self.default_model_combo)
        layout.addLayout(model_layout)

        # 说明文本
        info_label = QLabel("模型管理功能已集成到下方的表格中，支持搜索、拉取、删除和创建模型。")
        info_label.setStyleSheet("""
            QLabel {
                font-weight: normal;
                font-size: 12px;
                color: #7f8c8d;
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
                font-style: italic;
            }
        """)
        layout.addWidget(info_label)

        return self.ollama_group

    def create_info_group(self):
        """创建应用信息组"""
        self.info_group = QGroupBox(self.app.get_text("app_info"))
        self.info_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(self.info_group)

        self.info_label = QLabel()
        self.info_label.setStyleSheet("""
            QLabel {
                font-weight: normal;
                font-size: 12px;
                color: #555555;
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }
        """)
        layout.addWidget(self.info_label)

        # 初始化信息显示
        self.update_info_display()

        return self.info_group

    def on_language_change(self, language):
        """语言改变事件"""
        self.app.update_setting('language', language)
        self.app.update_language(language)
        self.update_info_display()
        QMessageBox.information(self, self.app.get_text("settings"),
                               f"{self.app.get_text('language_changed')} {language}")

    def on_theme_change(self, theme):
        """主题改变事件"""
        self.app.update_setting('theme', theme)
        self.app.apply_theme(theme)
        self.update_info_display()
        QMessageBox.information(self, self.app.get_text("settings"),
                               f"{self.app.get_text('theme_changed')} {theme}")

    def on_auto_save_change(self, checked):
        """自动保存设置改变事件"""
        self.app.update_setting('auto_save', checked)
        self.update_info_display()
        message = self.app.get_text("auto_save_enabled") if checked else self.app.get_text("auto_save_disabled")
        QMessageBox.information(self, self.app.get_text("settings"), message)

    def save_settings(self):
        """手动保存设置"""
        if self.app.save_settings():
            QMessageBox.information(self, self.app.get_text("settings"), self.app.get_text("settings_saved"))
        else:
            QMessageBox.critical(self, self.app.get_text("settings"), self.app.get_text("save_failed"))

    def reset_settings(self):
        """重置为默认设置"""
        reply = QMessageBox.question(self, self.app.get_text("settings"),
                                   self.app.get_text("confirm_reset"),
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # 重置设置
            self.app.app_settings = {
                "language": "中文",
                "theme": "现代",
                "window_size": "1200x800",
                "auto_save": True
            }

            # 更新界面
            self.lang_combo.setCurrentText("中文")
            self.theme_combo.setCurrentText("现代")
            self.auto_save_check.setChecked(True)

            # 应用主题和语言
            self.app.update_language("中文")
            self.app.apply_theme("现代")

            # 保存设置
            self.app.save_settings()
            self.update_info_display()

            QMessageBox.information(self, self.app.get_text("settings"),
                                   self.app.get_text("reset_success"))

    def export_settings(self):
        """导出设置到文件"""
        # 生成默认文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"settings_backup_{timestamp}.json"

        # 选择保存位置
        filename, _ = QFileDialog.getSaveFileName(
            self, self.app.get_text("export_settings_title"), default_filename, "JSON files (*.json)")

        if filename:
            try:
                # 添加导出信息
                export_data = {
                    "export_info": {
                        "timestamp": datetime.datetime.now().isoformat(),
                        "version": "1.0.0",
                        "app": self.app.get_text("app_title")
                    },
                    "settings": self.app.app_settings
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, self.app.get_text("settings"),
                                       f"{self.app.get_text('export_success')}\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, self.app.get_text("settings"),
                                    f"{self.app.get_text('export_failed')}\n{str(e)}")

    def update_info_display(self):
        """更新信息显示"""
        auto_save_status = self.app.get_text("enabled") if self.app.app_settings.get('auto_save', True) else self.app.get_text("disabled")

        info_text = f"""{self.app.get_text("app_name")}
{self.app.get_text("app_version")}
{self.app.get_text("settings_file")} {self.app.settings_file}
{self.app.get_text("current_theme")} {self.app.app_settings.get('theme', '现代')}
{self.app.get_text("current_language")} {self.app.app_settings.get('language', '中文')}
{self.app.get_text("auto_save_status")} {auto_save_status}"""

        self.info_label.setText(info_text)

    def on_ollama_server_change(self, text):
        """Ollama 服务器地址改变事件"""
        self.app.update_setting('ollama_server', text)
        # 同步到表格组件
        if hasattr(self, 'ollama_table'):
            self.ollama_table.set_server_url(text)

    def on_default_model_change(self, model):
        """默认模型改变事件"""
        self.app.update_setting('default_model', model)



    def refresh_texts(self):
        """刷新页面文本"""
        # 更新页面标题
        self.title.setText(self.app.get_text("settings"))

        # 更新组框标题
        self.basic_group.setTitle(self.app.get_text("basic_settings"))
        self.ollama_group.setTitle(self.app.get_text("ollama_settings"))
        self.info_group.setTitle(self.app.get_text("app_info"))

        # 更新标签文本
        self.lang_label.setText(self.app.get_text("language"))
        self.theme_label.setText(self.app.get_text("theme"))
        self.auto_save_label.setText(self.app.get_text("auto_save"))

        # 更新 Ollama 设置标签
        self.ollama_server_label.setText(self.app.get_text("server_address"))
        self.default_model_label.setText(self.app.get_text("default_model"))

        # 更新按钮文本
        self.save_btn.setText(self.app.get_text("save_settings"))
        self.reset_btn.setText(self.app.get_text("reset_default"))
        self.export_btn.setText(self.app.get_text("export_settings"))

        # Ollama 管理功能已集成到表格组件中

        # 更新信息显示
        self.update_info_display()

    def cleanup_threads(self):
        """清理工作线程"""
        # 清理模型获取线程
        if hasattr(self, 'model_worker') and self.model_worker:
            if self.model_worker.isRunning():
                self.model_worker.terminate()
                self.model_worker.wait(1000)  # 等待最多1秒
            self.model_worker.deleteLater()
            self.model_worker = None

        # 清理管理操作线程
        if hasattr(self, 'management_worker') and self.management_worker:
            if self.management_worker.isRunning():
                self.management_worker.terminate()
                self.management_worker.wait(1000)
            self.management_worker.deleteLater()
            self.management_worker = None


class OllamaWorker(QThread):
    """Ollama API 请求工作线程"""

    # 定义信号
    response_received = Signal(str)  # 成功响应
    error_occurred = Signal(str)     # 错误信息
    progress_updated = Signal(str)   # 进度更新

    def __init__(self, server_url, model=None, prompt=None):
        super().__init__()
        self.server_url = server_url
        self.model = model
        self.prompt = prompt

        # 设置线程在完成后自动删除
        self.finished.connect(self.deleteLater)

    def run(self):
        """执行 API 请求"""
        try:
            self.progress_updated.emit("正在连接到 Ollama 服务...")

            # 构建请求数据
            data = {
                "model": self.model,
                "prompt": self.prompt,
                "stream": False
            }

            self.progress_updated.emit("正在发送请求...")

            # 发送 POST 请求
            response = requests.post(
                f"{self.server_url}/api/generate",
                json=data,
                timeout=60  # 60秒超时
            )

            self.progress_updated.emit("正在处理响应...")

            if response.status_code == 200:
                result = response.json()
                if "response" in result:
                    self.response_received.emit(result["response"])
                else:
                    self.error_occurred.emit("响应格式错误：未找到 'response' 字段")
            else:
                self.error_occurred.emit(f"请求失败，状态码: {response.status_code}")

        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("连接失败，请检查 Ollama 服务是否启动")
        except requests.exceptions.Timeout:
            self.error_occurred.emit("请求超时，请检查网络连接或模型是否存在")
        except requests.exceptions.RequestException as e:
            self.error_occurred.emit(f"请求异常: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"未知错误: {str(e)}")




class OllamaModelWorker(QThread):
    """获取 Ollama 模型列表的工作线程"""

    # 定义信号
    models_received = Signal(list)   # 成功获取模型列表
    error_occurred = Signal(str)     # 错误信息
    progress_updated = Signal(str)   # 进度更新

    def __init__(self, server_url):
        super().__init__()
        self.server_url = server_url

        # 设置线程在完成后自动删除
        self.finished.connect(self.deleteLater)

    def run(self):
        """获取模型列表"""
        try:
            self.progress_updated.emit("正在获取模型列表...")

            # 发送 GET 请求获取模型列表
            response = requests.get(
                f"{self.server_url}/api/tags",
                timeout=10  # 10秒超时
            )

            if response.status_code == 200:
                result = response.json()
                if "models" in result:
                    models = []
                    for model_info in result["models"]:
                        if "name" in model_info:
                            models.append(model_info["name"])

                    if models:
                        self.models_received.emit(models)
                        self.progress_updated.emit(f"成功获取 {len(models)} 个模型")
                    else:
                        self.error_occurred.emit("未找到任何已安装的模型")
                else:
                    self.error_occurred.emit("响应格式错误：未找到 'models' 字段")
            else:
                self.error_occurred.emit(f"获取模型列表失败，状态码: {response.status_code}")

        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("连接失败，请检查 Ollama 服务是否启动")
        except requests.exceptions.Timeout:
            self.error_occurred.emit("请求超时，请检查网络连接")
        except requests.exceptions.RequestException as e:
            self.error_occurred.emit(f"请求异常: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"未知错误: {str(e)}")




class OllamaManagementWorker(QThread):
    """Ollama 模型管理工作线程"""

    # 定义信号
    operation_completed = Signal(str)  # 操作完成
    error_occurred = Signal(str)       # 错误信息
    progress_updated = Signal(str)     # 进度更新

    def __init__(self, server_url, operation, **kwargs):
        super().__init__()
        self.server_url = server_url
        self.operation = operation
        self.kwargs = kwargs

        # 设置线程在完成后自动删除
        self.finished.connect(self.deleteLater)

    def run(self):
        """执行模型管理操作"""
        try:
            if self.operation == "delete":
                self.delete_model()
            elif self.operation == "create":
                self.create_model()
        except Exception as e:
            self.error_occurred.emit(f"操作失败: {str(e)}")

    def delete_model(self):
        """删除模型"""
        model_name = self.kwargs.get("model_name")
        if not model_name:
            self.error_occurred.emit("模型名称不能为空")
            return

        self.progress_updated.emit(f"正在删除模型: {model_name}")

        try:
            response = requests.delete(
                f"{self.server_url}/api/delete",
                json={"name": model_name},
                timeout=30
            )

            if response.status_code == 200:
                self.operation_completed.emit(f"模型 {model_name} 删除成功")
            else:
                self.error_occurred.emit(f"删除失败，状态码: {response.status_code}")

        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("连接失败，请检查 Ollama 服务是否启动")
        except requests.exceptions.Timeout:
            self.error_occurred.emit("请求超时")
        except Exception as e:
            self.error_occurred.emit(f"删除模型时发生错误: {str(e)}")

    def create_model(self):
        """创建自定义模型"""
        model_name = self.kwargs.get("model_name")
        modelfile = self.kwargs.get("modelfile")

        if not model_name or not modelfile:
            self.error_occurred.emit("模型名称和模型文件不能为空")
            return

        self.progress_updated.emit(f"正在创建模型: {model_name}")

        try:
            response = requests.post(
                f"{self.server_url}/api/create",
                json={
                    "name": model_name,
                    "modelfile": modelfile
                },
                timeout=300  # 创建模型可能需要较长时间
            )

            if response.status_code == 200:
                self.operation_completed.emit(f"模型 {model_name} 创建成功")
            else:
                self.error_occurred.emit(f"创建失败，状态码: {response.status_code}")

        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("连接失败，请检查 Ollama 服务是否启动")
        except requests.exceptions.Timeout:
            self.error_occurred.emit("请求超时，模型创建可能需要较长时间")
        except Exception as e:
            self.error_occurred.emit(f"创建模型时发生错误: {str(e)}")


class OllamaPullWorker(QThread):
    """Ollama 模型拉取工作线程"""

    # 定义信号
    pull_completed = Signal(str)     # 拉取完成
    error_occurred = Signal(str)     # 错误信息
    progress_updated = Signal(str)   # 进度更新

    def __init__(self, server_url, model_name):
        super().__init__()
        self.server_url = server_url
        self.model_name = model_name

        # 设置线程在完成后自动删除
        self.finished.connect(self.deleteLater)

    def run(self):
        """执行模型拉取"""
        try:
            self.progress_updated.emit(f"正在拉取模型: {self.model_name}")

            response = requests.post(
                f"{self.server_url}/api/pull",
                json={"name": self.model_name},
                timeout=600  # 拉取模型可能需要很长时间
            )

            if response.status_code == 200:
                self.pull_completed.emit(f"模型 {self.model_name} 拉取成功")
            else:
                self.error_occurred.emit(f"拉取失败，状态码: {response.status_code}")

        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("连接失败，请检查 Ollama 服务是否启动")
        except requests.exceptions.Timeout:
            self.error_occurred.emit("拉取超时，请检查网络连接")
        except Exception as e:
            self.error_occurred.emit(f"拉取模型时发生错误: {str(e)}")


class OllamaModelTableWidget(QWidget):
    """Ollama 模型管理表格组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.server_url = "http://localhost:11434"
        self.local_models = []
        self.remote_models = []
        self.filtered_models = []
        self.workers = []  # 存储工作线程

        self.init_ui()
        self.init_remote_models()
        # 不在初始化时自动刷新模型，避免线程警告

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title = QLabel("Ollama 模型管理")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)

        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索模型:")
        search_label.setStyleSheet("font-size: 14px; font-weight: 500;")

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入模型名称进行搜索...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        self.search_input.textChanged.connect(self.filter_models)

        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))
        refresh_btn.clicked.connect(self.refresh_models)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, 1)
        search_layout.addWidget(refresh_btn)
        layout.addLayout(search_layout)

        # 表格
        self.table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.table)

        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("color: #7f8c8d; font-size: 12px; font-style: italic;")
        layout.addWidget(self.status_label)

    def setup_table(self):
        """设置表格"""
        # 设置列
        columns = ["模型名称", "来源", "大小", "状态", "操作"]
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)

        # 表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                gridline-color: #ecf0f1;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
        """)

        # 表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.setSortingEnabled(True)

        # 列宽设置
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 模型名称
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 来源
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 大小
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 状态
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 操作

    def init_remote_models(self):
        """初始化远程模型列表（模拟数据）"""
        self.remote_models = [
            {"name": "llama3:latest", "size": "4.7GB"},
            {"name": "llama3:8b", "size": "4.7GB"},
            {"name": "llama3:70b", "size": "40GB"},
            {"name": "gemma:latest", "size": "5.0GB"},
            {"name": "gemma:2b", "size": "1.4GB"},
            {"name": "gemma:7b", "size": "5.0GB"},
            {"name": "codellama:latest", "size": "3.8GB"},
            {"name": "codellama:7b", "size": "3.8GB"},
            {"name": "codellama:13b", "size": "7.3GB"},
            {"name": "mistral:latest", "size": "4.1GB"},
            {"name": "mistral:7b", "size": "4.1GB"},
            {"name": "phi3:latest", "size": "2.3GB"},
            {"name": "phi3:mini", "size": "2.3GB"},
            {"name": "phi3:medium", "size": "7.9GB"},
            {"name": "qwen:latest", "size": "4.2GB"},
            {"name": "qwen:7b", "size": "4.2GB"},
            {"name": "qwen:14b", "size": "8.2GB"},
        ]

    def refresh_models(self):
        """刷新模型列表"""
        self.status_label.setText("正在获取模型列表...")

        # 获取本地模型
        self.model_worker = OllamaModelWorker(self.server_url)
        self.model_worker.models_received.connect(self.on_local_models_received)
        self.model_worker.error_occurred.connect(self.on_models_error)
        self.model_worker.finished.connect(self.on_models_finished)
        self.model_worker.start()

    def on_local_models_received(self, models):
        """处理获取到的本地模型"""
        self.local_models = models
        self.update_table()
        self.status_label.setText(f"✅ 已获取 {len(models)} 个本地模型")

    def on_models_error(self, error):
        """处理模型获取错误"""
        self.local_models = []
        self.update_table()
        self.status_label.setText(f"❌ 获取模型失败: {error}")

    def on_models_finished(self):
        """模型获取完成"""
        if hasattr(self, 'model_worker') and self.model_worker:
            self.model_worker.deleteLater()
            self.model_worker = None

    def update_table(self):
        """更新表格数据"""
        # 合并本地和远程模型
        all_models = []

        # 添加本地模型
        for model in self.local_models:
            all_models.append({
                "name": model,
                "source": "本地",
                "size": "已下载",
                "status": "已下载",
                "is_local": True
            })

        # 添加远程模型（排除已下载的）
        for model in self.remote_models:
            if model["name"] not in self.local_models:
                all_models.append({
                    "name": model["name"],
                    "source": "远程",
                    "size": model["size"],
                    "status": "可下载",
                    "is_local": False
                })

        self.filtered_models = all_models
        self.filter_models()

    def filter_models(self):
        """过滤模型"""
        search_text = self.search_input.text().lower()

        if search_text:
            filtered = [model for model in self.filtered_models
                       if search_text in model["name"].lower()]
        else:
            filtered = self.filtered_models

        self.populate_table(filtered)

    def populate_table(self, models):
        """填充表格"""
        self.table.setRowCount(len(models))

        for row, model in enumerate(models):
            # 模型名称
            name_item = QTableWidgetItem(model["name"])
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 0, name_item)

            # 来源
            source_item = QTableWidgetItem(model["source"])
            source_item.setFlags(source_item.flags() & ~Qt.ItemIsEditable)
            if model["source"] == "本地":
                source_item.setBackground(QColor(144, 238, 144))  # 浅绿色
            else:
                source_item.setBackground(QColor(173, 216, 230))  # 浅蓝色
            self.table.setItem(row, 1, source_item)

            # 大小
            size_item = QTableWidgetItem(model["size"])
            size_item.setFlags(size_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 2, size_item)

            # 状态
            status_item = QTableWidgetItem(model["status"])
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            if model["status"] == "已下载":
                status_item.setBackground(QColor(144, 238, 144))  # 浅绿色
            else:
                status_item.setBackground(QColor(255, 255, 224))  # 浅黄色
            self.table.setItem(row, 3, status_item)

            # 操作按钮
            self.create_action_buttons(row, model)

    def create_action_buttons(self, row, model):
        """创建操作按钮"""
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(5, 2, 5, 2)
        button_layout.setSpacing(5)

        if model["is_local"]:
            # 本地模型：显示删除按钮
            delete_btn = QPushButton("🗑️ 删除")
            delete_btn.setStyleSheet(self.get_small_button_style("#e74c3c", "#c0392b"))
            delete_btn.clicked.connect(lambda: self.delete_model(model["name"]))
            button_layout.addWidget(delete_btn)
        else:
            # 远程模型：显示拉取按钮
            pull_btn = QPushButton("⬇️ 拉取")
            pull_btn.setStyleSheet(self.get_small_button_style("#27ae60", "#229954"))
            pull_btn.clicked.connect(lambda: self.pull_model(model["name"]))
            button_layout.addWidget(pull_btn)

        # 创建模型按钮（所有模型都有）
        create_btn = QPushButton("🛠️ 创建")
        create_btn.setStyleSheet(self.get_small_button_style("#f39c12", "#e67e22"))
        create_btn.clicked.connect(lambda: self.create_custom_model(model["name"]))
        button_layout.addWidget(create_btn)

        self.table.setCellWidget(row, 4, button_widget)

    def pull_model(self, model_name):
        """拉取模型"""
        reply = QMessageBox.question(self, "确认拉取",
                                   f"确定要拉取模型 '{model_name}' 吗？\n这可能需要较长时间。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.status_label.setText(f"正在拉取模型: {model_name}")

            # 创建拉取工作线程
            pull_worker = OllamaPullWorker(self.server_url, model_name)
            pull_worker.pull_completed.connect(self.on_pull_completed)
            pull_worker.error_occurred.connect(self.on_pull_error)
            pull_worker.finished.connect(lambda: self.on_worker_finished(pull_worker))

            self.workers.append(pull_worker)
            pull_worker.start()

    def delete_model(self, model_name):
        """删除模型"""
        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除模型 '{model_name}' 吗？\n此操作不可撤销。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.status_label.setText(f"正在删除模型: {model_name}")

            # 创建删除工作线程
            delete_worker = OllamaManagementWorker(self.server_url, "delete", model_name=model_name)
            delete_worker.operation_completed.connect(self.on_delete_completed)
            delete_worker.error_occurred.connect(self.on_delete_error)
            delete_worker.finished.connect(lambda: self.on_worker_finished(delete_worker))

            self.workers.append(delete_worker)
            delete_worker.start()

    def create_custom_model(self, base_model):
        """创建自定义模型"""
        from PySide6.QtWidgets import QDialog, QTextEdit

        # 创建对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("创建自定义模型")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)

        # 模型名称输入
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("模型名称:"))
        name_input = QLineEdit()
        name_input.setPlaceholderText("输入新模型的名称")
        name_layout.addWidget(name_input)
        layout.addLayout(name_layout)

        # 模型文件输入
        layout.addWidget(QLabel("模型文件 (Modelfile):"))
        modelfile_input = QTextEdit()
        modelfile_input.setPlainText(f"""FROM {base_model}
SYSTEM You are a helpful assistant.
PARAMETER temperature 0.7
PARAMETER top_p 0.9""")
        layout.addWidget(modelfile_input)

        # 按钮
        button_layout = QHBoxLayout()
        create_btn = QPushButton("创建")
        create_btn.setStyleSheet(self.get_button_style("#27ae60", "#229954"))
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet(self.get_button_style("#95a5a6", "#7f8c8d"))

        create_btn.clicked.connect(lambda: self.do_create_custom_model(
            dialog, name_input.text(), modelfile_input.toPlainText()))
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(create_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        dialog.exec()

    def do_create_custom_model(self, dialog, model_name, modelfile):
        """执行自定义模型创建"""
        if not model_name.strip() or not modelfile.strip():
            QMessageBox.warning(dialog, "警告", "模型名称和模型文件不能为空")
            return

        dialog.accept()
        self.status_label.setText(f"正在创建模型: {model_name}")

        # 创建模型工作线程
        create_worker = OllamaManagementWorker(
            self.server_url, "create", model_name=model_name, modelfile=modelfile)
        create_worker.operation_completed.connect(self.on_create_completed)
        create_worker.error_occurred.connect(self.on_create_error)
        create_worker.finished.connect(lambda: self.on_worker_finished(create_worker))

        self.workers.append(create_worker)
        create_worker.start()

    def on_pull_completed(self, message):
        """拉取完成"""
        QMessageBox.information(self, "成功", message)
        self.refresh_models()

    def on_pull_error(self, error):
        """拉取错误"""
        QMessageBox.critical(self, "错误", f"拉取失败: {error}")
        self.status_label.setText("准备就绪")

    def on_delete_completed(self, message):
        """删除完成"""
        QMessageBox.information(self, "成功", message)
        self.refresh_models()

    def on_delete_error(self, error):
        """删除错误"""
        QMessageBox.critical(self, "错误", f"删除失败: {error}")
        self.status_label.setText("准备就绪")

    def on_create_completed(self, message):
        """创建完成"""
        QMessageBox.information(self, "成功", message)
        self.refresh_models()

    def on_create_error(self, error):
        """创建错误"""
        QMessageBox.critical(self, "错误", f"创建失败: {error}")
        self.status_label.setText("准备就绪")

    def on_worker_finished(self, worker):
        """工作线程完成"""
        if worker in self.workers:
            self.workers.remove(worker)
            worker.deleteLater()

        if not self.workers:  # 所有工作线程完成
            self.status_label.setText("准备就绪")

    def get_button_style(self, color1, color2):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
            QPushButton:pressed {{
                background: {color2};
            }}
        """

    def get_small_button_style(self, color1, color2):
        """获取小按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                font-weight: 500;
                min-width: 50px;
                max-height: 24px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
            QPushButton:pressed {{
                background: {color2};
            }}
        """

    def set_server_url(self, url):
        """设置服务器地址"""
        self.server_url = url
        self.refresh_models()

    def cleanup_all_threads(self):
        """清理所有工作线程"""
        # 清理操作线程
        for worker in self.workers[:]:  # 创建副本避免修改列表时的问题
            if worker.isRunning():
                worker.terminate()
                worker.wait(1000)  # 等待最多1秒
            worker.deleteLater()
        self.workers.clear()

        # 清理模型获取线程
        if hasattr(self, 'model_worker') and self.model_worker:
            if self.model_worker.isRunning():
                self.model_worker.terminate()
                self.model_worker.wait(1000)
            self.model_worker.deleteLater()
            self.model_worker = None

    def closeEvent(self, event):
        """关闭事件，清理工作线程"""
        self.cleanup_all_threads()
        event.accept()


class OllamaManagerPage(BasePage):
    """Ollama 模型管理页面"""

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 页面标题
        title = QLabel("Ollama 模型管理")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)

        # 服务器配置区域
        config_group = QGroupBox("服务器配置")
        config_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        config_layout = QHBoxLayout(config_group)
        config_layout.setSpacing(10)

        server_label = QLabel("服务器地址:")
        server_label.setStyleSheet("font-weight: normal; font-size: 14px;")

        self.server_input = QLineEdit()
        self.server_input.setText(self.app.app_settings.get('ollama_server', 'http://localhost:11434'))
        self.server_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        self.server_input.textChanged.connect(self.on_server_change)

        config_layout.addWidget(server_label)
        config_layout.addWidget(self.server_input, 1)
        layout.addWidget(config_group)

        # 模型管理表格
        self.ollama_table = OllamaModelTableWidget()
        self.ollama_table.set_server_url(self.server_input.text())
        layout.addWidget(self.ollama_table)

    def on_server_change(self, text):
        """服务器地址改变事件"""
        self.app.update_setting('ollama_server', text)
        if hasattr(self, 'ollama_table'):
            self.ollama_table.set_server_url(text)

    def refresh_texts(self):
        """刷新页面文本（多语言支持）"""
        pass

    def cleanup_threads(self):
        """清理工作线程"""
        if hasattr(self, 'ollama_table') and self.ollama_table:
            # 清理表格组件中的所有工作线程
            for worker in self.ollama_table.workers:
                if worker.isRunning():
                    worker.terminate()
                    worker.wait(1000)
                worker.deleteLater()
            self.ollama_table.workers.clear()

            # 清理模型获取线程
            if hasattr(self.ollama_table, 'model_worker') and self.ollama_table.model_worker:
                if self.ollama_table.model_worker.isRunning():
                    self.ollama_table.model_worker.terminate()
                    self.ollama_table.model_worker.wait(1000)
                self.ollama_table.model_worker.deleteLater()
                self.ollama_table.model_worker = None


class OllamaTestPage(BasePage):
    """Ollama 测试页面"""

    def __init__(self, app):
        super().__init__(app)
        self.worker = None  # 生成请求工作线程
        self.model_worker = None  # 获取模型列表工作线程

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        self.title = QLabel("Ollama 模型测试")
        self.title.setObjectName("pageTitle")
        self.title.setStyleSheet("""
            QLabel#pageTitle {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.title)

        # 配置区域
        config_group = self.create_config_group()
        layout.addWidget(config_group)

        # 输入区域
        input_group = self.create_input_group()
        layout.addWidget(input_group)

        # 控制按钮
        control_layout = self.create_control_buttons()
        layout.addLayout(control_layout)

        # 结果显示区域
        result_group = self.create_result_group()
        layout.addWidget(result_group)

    def create_config_group(self):
        """创建配置区域"""
        group = QGroupBox("模型配置")
        group.setStyleSheet(self.get_group_style())

        layout = QVBoxLayout(group)
        layout.setSpacing(15)

        # 服务器地址配置
        server_layout = QHBoxLayout()
        server_label = QLabel("服务器地址:")
        server_label.setStyleSheet("font-weight: normal; font-size: 14px; min-width: 80px;")

        self.server_input = QLineEdit("http://localhost:11434")
        self.server_input.setStyleSheet(self.get_input_style())

        server_layout.addWidget(server_label)
        server_layout.addWidget(self.server_input)
        layout.addLayout(server_layout)

        # 模型选择
        model_layout = QHBoxLayout()
        model_label = QLabel("选择模型:")
        model_label.setStyleSheet("font-weight: normal; font-size: 14px; min-width: 80px;")

        # 模型下拉框和刷新按钮的容器
        model_container = QHBoxLayout()

        self.model_combo = QComboBox()
        # 添加默认模型列表（作为备选）
        self.default_models = [
            "llama3", "llama3:8b", "llama3:70b",
            "gemma", "gemma:2b", "gemma:7b",
            "codellama", "codellama:7b", "codellama:13b",
            "mistral", "mistral:7b",
            "qwen", "qwen:7b", "qwen:14b",
            "phi3", "phi3:mini", "phi3:medium"
        ]
        self.model_combo.addItems(self.default_models)
        self.model_combo.setCurrentText("llama3")
        self.model_combo.setStyleSheet(self.get_combo_style())

        # 刷新模型列表按钮
        self.refresh_models_btn = QPushButton("🔄")
        self.refresh_models_btn.setToolTip("刷新模型列表")
        self.refresh_models_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 30px;
                max-width: 30px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.refresh_models_btn.clicked.connect(self.refresh_model_list)

        model_container.addWidget(self.model_combo)
        model_container.addWidget(self.refresh_models_btn)

        model_layout.addWidget(model_label)
        model_layout.addLayout(model_container)
        layout.addLayout(model_layout)

        # 模型状态标签
        self.model_status_label = QLabel("点击 🔄 获取本地模型列表")
        self.model_status_label.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        layout.addWidget(self.model_status_label)

        return group

    def create_input_group(self):
        """创建输入区域"""
        group = QGroupBox("输入提示词")
        group.setStyleSheet(self.get_group_style())

        layout = QVBoxLayout(group)

        # 提示词输入框
        self.prompt_input = QPlainTextEdit()
        self.prompt_input.setPlaceholderText("请输入您想要发送给模型的提示词...\n\n例如：\n- 你好，请介绍一下你自己\n- 请写一个Python函数来计算斐波那契数列\n- 解释一下什么是机器学习")
        self.prompt_input.setStyleSheet(self.get_text_edit_style())
        self.prompt_input.setMinimumHeight(120)
        self.prompt_input.setMaximumHeight(200)

        layout.addWidget(self.prompt_input)

        return group

    def create_control_buttons(self):
        """创建控制按钮"""
        layout = QHBoxLayout()

        # 发送请求按钮
        self.send_button = QPushButton("🚀 发送请求")
        self.send_button.setStyleSheet(self.get_button_style("#27ae60", "#229954"))
        self.send_button.clicked.connect(self.send_request)

        # 清空输入按钮
        clear_input_btn = QPushButton("🗑️ 清空输入")
        clear_input_btn.setStyleSheet(self.get_button_style("#f39c12", "#e67e22"))
        clear_input_btn.clicked.connect(self.clear_input)

        # 清空输出按钮
        clear_output_btn = QPushButton("🧹 清空输出")
        clear_output_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))
        clear_output_btn.clicked.connect(self.clear_output)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-size: 12px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #7f8c8d; font-size: 12px; font-style: italic;")

        layout.addWidget(self.send_button)
        layout.addWidget(clear_input_btn)
        layout.addWidget(clear_output_btn)
        layout.addStretch()
        layout.addWidget(self.status_label)
        layout.addWidget(self.progress_bar)

        return layout

    def create_result_group(self):
        """创建结果显示区域"""
        group = QGroupBox("模型响应")
        group.setStyleSheet(self.get_group_style())

        layout = QVBoxLayout(group)

        # 响应显示区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setPlaceholderText("模型的响应将在这里显示...")
        self.result_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                font-size: 14px;
                line-height: 1.6;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        self.result_text.setMinimumHeight(300)

        layout.addWidget(self.result_text)

        return group

    def get_group_style(self):
        """获取组框样式"""
        return """
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """

    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """

    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
                background-color: #ffffff;
                min-width: 150px;
            }
            QComboBox:hover {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 5px;
            }
        """

    def get_text_edit_style(self):
        """获取文本编辑框样式"""
        return """
            QPlainTextEdit {
                background-color: #ffffff;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                font-size: 14px;
                line-height: 1.5;
            }
            QPlainTextEdit:focus {
                border-color: #3498db;
            }
        """

    def get_button_style(self, color1, color2):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
            QPushButton:pressed {{
                background: {color2};
            }}
            QPushButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
            }}
        """

    def refresh_model_list(self):
        """刷新模型列表"""
        server_url = self.server_input.text().strip()
        if not server_url:
            QMessageBox.warning(self, "警告", "请先输入服务器地址")
            return

        # 禁用刷新按钮
        self.refresh_models_btn.setEnabled(False)
        self.refresh_models_btn.setText("⏳")
        self.model_status_label.setText("正在获取模型列表...")

        # 创建并启动模型获取工作线程
        self.model_worker = OllamaModelWorker(server_url)
        self.model_worker.models_received.connect(self.on_models_received)
        self.model_worker.error_occurred.connect(self.on_model_error)
        self.model_worker.progress_updated.connect(self.on_model_progress_updated)
        self.model_worker.finished.connect(self.on_model_request_finished)
        self.model_worker.start()

    def on_models_received(self, models):
        """处理获取到的模型列表"""
        # 保存当前选择的模型
        current_model = self.model_combo.currentText()

        # 清空并更新模型列表
        self.model_combo.clear()
        self.model_combo.addItems(models)

        # 尝试恢复之前的选择
        if current_model in models:
            self.model_combo.setCurrentText(current_model)
        elif models:
            self.model_combo.setCurrentIndex(0)

        self.model_status_label.setText(f"✅ 已获取 {len(models)} 个本地模型")

    def on_model_error(self, error_message):
        """处理模型获取错误"""
        self.model_status_label.setText(f"❌ 获取失败: {error_message}")

        # 恢复默认模型列表
        current_model = self.model_combo.currentText()
        self.model_combo.clear()
        self.model_combo.addItems(self.default_models)

        if current_model in self.default_models:
            self.model_combo.setCurrentText(current_model)

        # 显示错误提示
        QMessageBox.warning(self, "获取模型失败",
                           f"无法获取本地模型列表，已恢复默认列表\n\n错误详情：{error_message}")

    def on_model_progress_updated(self, message):
        """更新模型获取进度"""
        self.model_status_label.setText(message)

    def on_model_request_finished(self):
        """模型获取请求完成"""
        self.refresh_models_btn.setEnabled(True)
        self.refresh_models_btn.setText("🔄")

        # 清理工作线程
        if self.model_worker:
            self.model_worker.deleteLater()
            self.model_worker = None

    def send_request(self):
        """发送请求到 Ollama"""
        # 获取输入内容
        server_url = self.server_input.text().strip()
        model = self.model_combo.currentText()
        prompt = self.prompt_input.toPlainText().strip()

        # 验证输入
        if not server_url:
            QMessageBox.warning(self, "警告", "请输入服务器地址")
            return

        if not prompt:
            QMessageBox.warning(self, "警告", "请输入提示词")
            return

        # 禁用发送按钮，显示进度
        self.send_button.setEnabled(False)
        self.send_button.setText("🔄 发送中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("正在准备请求...")

        # 创建并启动工作线程
        self.worker = OllamaWorker(server_url, model, prompt)
        self.worker.response_received.connect(self.on_response_received)
        self.worker.error_occurred.connect(self.on_error_occurred)
        self.worker.progress_updated.connect(self.on_progress_updated)
        self.worker.finished.connect(self.on_request_finished)
        self.worker.start()

    def on_response_received(self, response):
        """处理成功响应"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        model = self.model_combo.currentText()
        prompt = self.prompt_input.toPlainText().strip()

        # 格式化显示结果
        formatted_result = f"""
{'='*60}
🕒 时间: {timestamp}
🤖 模型: {model}
📝 提示词: {prompt[:100]}{'...' if len(prompt) > 100 else ''}
{'='*60}

💬 模型响应:
{response}

{'='*60}
"""

        # 添加到结果显示区域
        self.result_text.append(formatted_result)

        # 滚动到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.result_text.setTextCursor(cursor)

        self.status_label.setText("✅ 请求完成")

    def on_error_occurred(self, error_message):
        """处理错误"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        error_result = f"""
{'='*60}
🕒 时间: {timestamp}
❌ 错误: {error_message}
{'='*60}
"""

        self.result_text.append(error_result)
        self.status_label.setText("❌ 请求失败")

        # 显示错误消息框
        QMessageBox.critical(self, "请求失败", f"请求失败，请检查 Ollama 服务是否启动\n\n错误详情：{error_message}")

    def on_progress_updated(self, message):
        """更新进度信息"""
        self.status_label.setText(message)

    def on_request_finished(self):
        """请求完成后的清理工作"""
        self.send_button.setEnabled(True)
        self.send_button.setText("🚀 发送请求")
        self.progress_bar.setVisible(False)

        # 如果状态还是"正在处理响应..."，更新为完成状态
        if "正在处理响应" in self.status_label.text():
            self.status_label.setText("✅ 请求完成")

        # 清理工作线程
        if self.worker:
            self.worker.deleteLater()
            self.worker = None

    def clear_input(self):
        """清空输入框"""
        self.prompt_input.clear()
        self.status_label.setText("输入已清空")

    def clear_output(self):
        """清空输出框"""
        self.result_text.clear()
        self.status_label.setText("输出已清空")

    def auto_refresh_models(self):
        """自动刷新模型列表（静默模式）"""
        server_url = self.server_input.text().strip()
        if not server_url:
            return

        # 静默获取模型列表，不显示错误提示
        self.model_worker = OllamaModelWorker(server_url)
        self.model_worker.models_received.connect(self.on_models_received_silent)
        self.model_worker.error_occurred.connect(self.on_model_error_silent)
        self.model_worker.finished.connect(self.on_model_request_finished)
        self.model_worker.start()

    def on_models_received_silent(self, models):
        """静默处理获取到的模型列表"""
        # 保存当前选择的模型
        current_model = self.model_combo.currentText()

        # 清空并更新模型列表
        self.model_combo.clear()
        self.model_combo.addItems(models)

        # 尝试恢复之前的选择
        if current_model in models:
            self.model_combo.setCurrentText(current_model)
        elif models:
            self.model_combo.setCurrentIndex(0)

        self.model_status_label.setText(f"✅ 已自动获取 {len(models)} 个本地模型")

    def on_model_error_silent(self, error_message):
        """静默处理模型获取错误"""
        self.model_status_label.setText("💡 点击 🔄 手动获取模型列表")

    def refresh_texts(self):
        """刷新页面文本（多语言支持）"""
        # 这里可以根据需要添加多语言支持
        pass

    def cleanup_threads(self):
        """清理工作线程"""
        # 清理生成请求线程
        if hasattr(self, 'worker') and self.worker:
            try:
                if self.worker.isRunning():
                    self.worker.terminate()
                    self.worker.wait(500)
                self.worker.deleteLater()
            except:
                pass
            self.worker = None

        # 清理模型获取线程
        if hasattr(self, 'model_worker') and self.model_worker:
            try:
                if self.model_worker.isRunning():
                    self.model_worker.terminate()
                    self.model_worker.wait(500)
                self.model_worker.deleteLater()
            except:
                pass
            self.model_worker = None

    def cleanup_threads(self):
        """清理工作线程（子类可重写）"""
        pass


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("视频创作工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("VideoCreator")

    # 创建主窗口
    window = ModernApp()
    window.show()

    # 设置退出时的清理
    def cleanup_on_exit():
        try:
            # 清理所有页面的线程
            for page in window.pages.values():
                if hasattr(page, 'cleanup_threads'):
                    page.cleanup_threads()
        except:
            pass

    app.aboutToQuit.connect(cleanup_on_exit)

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
